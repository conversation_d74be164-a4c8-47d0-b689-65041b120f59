# 信息源搜索功能更新说明

## 概述

本次更新从 `WeMediaController.selectPage` 接口入口开始，完整修改了信息源搜索功能，将原有的GET请求方式改为POST请求，并支持标准的JSON请求体结构。修改涉及完整的调用链，确保从Controller到底层Service的一致性。

## 调用链说明

### 修改前的调用链：
1. `WeMediaController.selectPage(WeMediaDTO)`
2. → `ElasticSearchAPI.selectInformation(WeMediaDTO)`
3. → 构建URL查询参数
4. → `WebSpiderInformationSourceDataServiceImpl.searchInformationSource(InformationSourceBean)` (GET请求)

### 修改后的调用链：
1. `WeMediaController.selectPage(WeMediaDTO)`
2. → `ElasticSearchAPI.selectInformation(WeMediaDTO)`
3. → `buildSearchRequestBody(WeMediaDTO)` 构建JSON请求体
4. → `WebSpiderInformationSourceDataServiceImpl.searchInformationSource(InformationSourceBean, JSONObject)` (POST请求)

## 主要改进

- **统一请求格式**：所有搜索条件统一使用JSON请求体
- **增强搜索能力**：支持更复杂的搜索条件组合
- **标准化接口**：符合RESTful API设计规范
- **向后兼容**：保留原有GET请求方式

## 主要修改

### 1. 新增方法重载

在 `WebSpiderInformationSourceDataServiceImpl` 类中新增了一个重载方法：

```java
@Override
public JSONObject searchInformationSource(InformationSourceBean informationSourceBean, JSONObject requestBody) throws Exception
```

### 2. 支持的JSON请求体结构

新方法支持以下JSON请求体结构：

```json
{
    "condition": {
        "k3Id": "XX",                    // 信息源ID
        "mediaTag": "XX",
        "k3IdName": "XX信息源名字",        // 信息源名称
        "organizer": "XXX主办单位",       // 主办单位
        "name": "账号名XXX",             // 账号名称
        "uid": "XXXXX",
        "mediaInfoTag": 0,               // 0: 非媒体, 1: 媒体
        "mediaLevel": "央级",            // 媒体级别: 央级、省级、市级、区级、其他
        "weiboVerifyType": "金V",        // 微博认证类型: 金V、橙V、黄V
        "verifyType": "政务",            // 账号认证类型: 政务、机构、企业、个人、未认证
        "industry": "XX行业",            // 行业
        "followersCountRangeFrom": 0,    // 粉丝数范围起始
        "followersCountRangeTo": 1000,   // 粉丝数范围结束
        "bigVLabel": 1,                  // 大V标签: 0=否, 1=是
        "ipLocation": "XX地址",          // IP位置
        "area": "XX地址",                // 信息源地区
        "status": 0                      // 实施状态
    },
    "paginator": {
        "from": 0,                       // 起始位置
        "size": 100,                     // 页面大小
        "sorts": [                       // 排序规则
            {
                "field": "created_at",   // 排序字段
                "order": "desc"          // 排序方向: asc/desc
            }
        ]
    }
}
```

### 3. 新增工具类

创建了 `InformationSourceRequestBuilder` 工具类，提供便捷的请求体构建方法：

- `InformationSourceRequestBuilder.condition()` - 构建搜索条件
- `InformationSourceRequestBuilder.paginator(from, size)` - 构建分页信息
- `InformationSourceRequestBuilder.buildSearchRequest(condition, paginator)` - 构建完整请求体

### 4. 请求体验证

新方法包含完整的请求体验证逻辑：
- 验证请求体不为空
- 验证必需的 `condition` 和 `paginator` 字段
- 验证分页参数的 `from` 和 `size` 字段

## 使用方式

### 方式1：使用构建器模式

```java
// 创建信息源配置
InformationSourceBean informationSourceBean = new InformationSourceBean();
informationSourceBean.setServiceUrl("http://your-api-server/api/search");

// 构建搜索条件
JSONObject condition = InformationSourceRequestBuilder.condition()
        .k3Id("12345")
        .k3IdName("测试信息源")
        .organizer("测试主办单位")
        .mediaLevel("央级")
        .verifyType("政务")
        .status(0)
        .build();

// 构建分页信息
JSONObject paginator = InformationSourceRequestBuilder.paginator(0, 100)
        .sorts("created_at", "desc")
        .build();

// 构建完整请求体
JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(condition, paginator);

// 调用新的搜索方法
JSONObject result = WebSpiderInformationSource.INSTANCE
        .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
        .searchInformationSource(informationSourceBean, requestBody);
```

### 方式2：直接构建JSON

```java
JSONObject requestBody = new JSONObject();

JSONObject condition = new JSONObject();
condition.put("k3Id", "XX");
condition.put("name", "账号名XXX");
condition.put("mediaLevel", "央级");
condition.put("status", 0);

JSONObject paginator = new JSONObject();
paginator.put("from", 0);
paginator.put("size", 100);

requestBody.put("condition", condition);
requestBody.put("paginator", paginator);

JSONObject result = WebSpiderInformationSource.INSTANCE
        .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
        .searchInformationSource(informationSourceBean, requestBody);
```

## 向后兼容性

原有的GET请求方式保持不变：

```java
// 原有方式仍然可用
JSONObject result = WebSpiderInformationSource.INSTANCE
        .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
        .searchInformationSource(informationSourceBean);
```

## 错误处理

新方法包含以下错误处理：
- `InvalidKeyException` - 当serviceUrl为空时抛出
- `InvalidKeyException` - 当请求体为空或缺少必需字段时抛出
- `Exception` - HTTP请求异常时抛出

## 文件修改清单

### 核心修改文件

1. **`src/main/java/com/izhonghong/ubc/information/elasticsearch/ElasticSearchAPI.java`**
   - 修改 `selectInformation` 方法，从构建URL参数改为构建JSON请求体
   - 新增 `buildSearchRequestBody` 私有方法，将WeMediaDTO转换为标准JSON请求体
   - 更新调用方式，使用新的POST请求方法

2. **`src/main/java/com/izhonghong/ubc/information/service/impl/abstracts/WebSpiderInformationSourceDataServiceImpl.java`**
   - 新增重载方法 `searchInformationSource(InformationSourceBean, JSONObject)`
   - 新增请求体验证方法 `validateRequestBody(JSONObject)`

3. **`src/main/java/com/izhonghong/ubc/information/service/abstracts/AbstractsInformationSourceDataService.java`**
   - 新增方法声明 `searchInformationSource(InformationSourceBean, JSONObject)`

### 新增工具和示例文件

4. **`src/main/java/com/izhonghong/ubc/information/util/InformationSourceRequestBuilder.java`** (新文件)
   - 请求体构建工具类
   - 提供链式调用的条件构建器和分页构建器

5. **`src/main/java/com/izhonghong/ubc/information/example/InformationSourceSearchExample.java`** (新文件)
   - 完整的使用示例代码
   - 展示多种搜索场景

### 测试文件

6. **`src/test/java/com/izhonghong/ubc/information/service/WebSpiderInformationSourceDataServiceTest.java`** (新文件)
   - 服务层单元测试

7. **`src/test/java/com/izhonghong/ubc/information/elasticsearch/ElasticSearchAPITest.java`** (新文件)
   - ElasticSearchAPI测试，重点测试JSON请求体构建

8. **`src/test/java/com/izhonghong/ubc/information/integration/WeMediaControllerIntegrationTest.java`** (新文件)
   - 从Controller到Service的完整集成测试

## 新增控制器接口

### 新增接口文件

9. **`src/main/java/com/izhonghong/ubc/information/controller/MediaLibraryController.java`**
   - 新增接口 `selectPageWithJson(@RequestBody InformationSourceSearchRequestDTO)`
   - 添加完整的请求体验证逻辑

10. **`src/main/java/com/izhonghong/ubc/information/elasticsearch/ElasticSearchAPI.java`**
    - 新增方法 `searchWithJson(InformationSourceSearchRequestDTO)`
    - 新增私有方法 `buildJsonRequestBody(InformationSourceSearchRequestDTO)`

11. **`src/main/java/com/izhonghong/ubc/information/entity/dto/InformationSourceSearchRequestDTO.java`** (新文件)
    - 请求体DTO类，包含ConditionDTO、PaginatorDTO、SortDTO内部类
    - 完整的Swagger注解支持

### 示例和文档文件

12. **`src/main/java/com/izhonghong/ubc/information/example/MediaLibraryControllerUsageExample.java`** (新文件)
    - 控制器接口使用示例代码
    - 包含curl命令示例

13. **`API_DOCUMENTATION.md`** (新文件)
    - 完整的API接口文档
    - 包含请求示例和响应格式

14. **`src/test/java/com/izhonghong/ubc/information/controller/MediaLibraryControllerTest.java`** (新文件)
    - 控制器层单元测试
    - 覆盖各种验证场景

## 新增API接口

### POST /mediaLibrary/selectPageWithJson

- **功能**: 媒体列表的分页查询 - 支持JSON请求体
- **请求方式**: POST
- **Content-Type**: application/json
- **特点**:
  - 支持复杂的搜索条件组合
  - 支持多字段排序
  - 完整的请求体验证
  - 标准的RESTful API设计

## 测试建议

建议编写以下测试用例：
1. 测试新方法的基本功能
2. 测试请求体验证逻辑
3. 测试各种搜索条件组合
4. 测试分页功能
5. 测试排序功能
6. 测试错误处理
7. 确保原有GET请求方式仍然正常工作
8. 测试控制器层的参数验证
9. 测试JSON序列化和反序列化
10. 进行完整的集成测试
