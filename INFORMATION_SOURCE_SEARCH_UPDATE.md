# 信息源搜索功能更新说明

## 概述

本次更新为 `WebSpiderInformationSourceDataServiceImpl` 类的 `searchInformationSource` 方法添加了对POST请求和JSON请求体的支持。原有的GET请求方式保持不变，确保向后兼容性。

## 主要修改

### 1. 新增方法重载

在 `WebSpiderInformationSourceDataServiceImpl` 类中新增了一个重载方法：

```java
@Override
public JSONObject searchInformationSource(InformationSourceBean informationSourceBean, JSONObject requestBody) throws Exception
```

### 2. 支持的JSON请求体结构

新方法支持以下JSON请求体结构：

```json
{
    "condition": {
        "k3Id": "XX",                    // 信息源ID
        "mediaTag": "XX",
        "k3IdName": "XX信息源名字",        // 信息源名称
        "organizer": "XXX主办单位",       // 主办单位
        "name": "账号名XXX",             // 账号名称
        "uid": "XXXXX",
        "mediaInfoTag": 0,               // 0: 非媒体, 1: 媒体
        "mediaLevel": "央级",            // 媒体级别: 央级、省级、市级、区级、其他
        "weiboVerifyType": "金V",        // 微博认证类型: 金V、橙V、黄V
        "verifyType": "政务",            // 账号认证类型: 政务、机构、企业、个人、未认证
        "industry": "XX行业",            // 行业
        "followersCountRangeFrom": 0,    // 粉丝数范围起始
        "followersCountRangeTo": 1000,   // 粉丝数范围结束
        "bigVLabel": 1,                  // 大V标签: 0=否, 1=是
        "ipLocation": "XX地址",          // IP位置
        "area": "XX地址",                // 信息源地区
        "status": 0                      // 实施状态
    },
    "paginator": {
        "from": 0,                       // 起始位置
        "size": 100,                     // 页面大小
        "sorts": [                       // 排序规则
            {
                "field": "created_at",   // 排序字段
                "order": "desc"          // 排序方向: asc/desc
            }
        ]
    }
}
```

### 3. 新增工具类

创建了 `InformationSourceRequestBuilder` 工具类，提供便捷的请求体构建方法：

- `InformationSourceRequestBuilder.condition()` - 构建搜索条件
- `InformationSourceRequestBuilder.paginator(from, size)` - 构建分页信息
- `InformationSourceRequestBuilder.buildSearchRequest(condition, paginator)` - 构建完整请求体

### 4. 请求体验证

新方法包含完整的请求体验证逻辑：
- 验证请求体不为空
- 验证必需的 `condition` 和 `paginator` 字段
- 验证分页参数的 `from` 和 `size` 字段

## 使用方式

### 方式1：使用构建器模式

```java
// 创建信息源配置
InformationSourceBean informationSourceBean = new InformationSourceBean();
informationSourceBean.setServiceUrl("http://your-api-server/api/search");

// 构建搜索条件
JSONObject condition = InformationSourceRequestBuilder.condition()
        .k3Id("12345")
        .k3IdName("测试信息源")
        .organizer("测试主办单位")
        .mediaLevel("央级")
        .verifyType("政务")
        .status(0)
        .build();

// 构建分页信息
JSONObject paginator = InformationSourceRequestBuilder.paginator(0, 100)
        .sorts("created_at", "desc")
        .build();

// 构建完整请求体
JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(condition, paginator);

// 调用新的搜索方法
JSONObject result = WebSpiderInformationSource.INSTANCE
        .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
        .searchInformationSource(informationSourceBean, requestBody);
```

### 方式2：直接构建JSON

```java
JSONObject requestBody = new JSONObject();

JSONObject condition = new JSONObject();
condition.put("k3Id", "XX");
condition.put("name", "账号名XXX");
condition.put("mediaLevel", "央级");
condition.put("status", 0);

JSONObject paginator = new JSONObject();
paginator.put("from", 0);
paginator.put("size", 100);

requestBody.put("condition", condition);
requestBody.put("paginator", paginator);

JSONObject result = WebSpiderInformationSource.INSTANCE
        .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
        .searchInformationSource(informationSourceBean, requestBody);
```

## 向后兼容性

原有的GET请求方式保持不变：

```java
// 原有方式仍然可用
JSONObject result = WebSpiderInformationSource.INSTANCE
        .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
        .searchInformationSource(informationSourceBean);
```

## 错误处理

新方法包含以下错误处理：
- `InvalidKeyException` - 当serviceUrl为空时抛出
- `InvalidKeyException` - 当请求体为空或缺少必需字段时抛出
- `Exception` - HTTP请求异常时抛出

## 文件修改清单

1. `src/main/java/com/izhonghong/ubc/information/service/impl/abstracts/WebSpiderInformationSourceDataServiceImpl.java`
   - 新增重载方法 `searchInformationSource(InformationSourceBean, JSONObject)`
   - 新增请求体验证方法 `validateRequestBody(JSONObject)`

2. `src/main/java/com/izhonghong/ubc/information/service/abstracts/AbstractsInformationSourceDataService.java`
   - 新增抽象方法声明

3. `src/main/java/com/izhonghong/ubc/information/util/InformationSourceRequestBuilder.java` (新文件)
   - 请求体构建工具类

4. `src/main/java/com/izhonghong/ubc/information/example/InformationSourceSearchExample.java` (新文件)
   - 使用示例代码

## 测试建议

建议编写以下测试用例：
1. 测试新方法的基本功能
2. 测试请求体验证逻辑
3. 测试各种搜索条件组合
4. 测试分页功能
5. 测试排序功能
6. 测试错误处理
7. 确保原有GET请求方式仍然正常工作
