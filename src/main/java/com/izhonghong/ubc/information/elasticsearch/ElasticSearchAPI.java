package com.izhonghong.ubc.information.elasticsearch;

import cn.cnhon.util.MD5;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izhonghong.ubc.information.common.enums.*;
import com.izhonghong.ubc.information.config.WebCrawlerConfig;
import com.izhonghong.ubc.information.constant.ArticleTypeEnum;
import com.izhonghong.ubc.information.constant.MediaCodeEnum;
import com.izhonghong.ubc.information.constant.SystemModuleEnum;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.entity.MediaLibraryInformationSourceBean;
import com.izhonghong.ubc.information.entity.OperationLog;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDeleteDTO;
import com.izhonghong.ubc.information.entity.vo.AccountInfoVo;
import com.izhonghong.ubc.information.service.MediaService;
import com.izhonghong.ubc.information.service.OperationLogService;
import com.izhonghong.ubc.information.util.StringUtil;
import com.izhonghong.ubc.information.util.bean.ReflectionUtils;
import com.izhonghong.ubc.information.util.components.ISearchDataAccess;
import com.izhonghong.ubc.information.util.components.SystemAlertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 对接数据组的es
 * <AUTHOR>
 *
 * ***/
@Slf4j
@Component
public class ElasticSearchAPI {

	@Autowired
	private WebCrawlerConfig  webCrawlerConfig;

	@Autowired
	private SystemAlertUtils systemAlertUtils;

	@Autowired
	private MediaService mediaService;

	@Autowired
    private OperationLogService operationLogService;

	@Value("${webcrawler.server.fullLibAccountPageUrl}")
	private String FullLibAccountPageUrl;

	/***
	 * 数据 的综合查询
	 *
	 * ***/
	public JSONObject search(MediaBaseDTO  mediaBase) {
		MediaLibraryDTO mediaLibraryDTO = (MediaLibraryDTO) mediaBase;
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		if(!StringUtils.isEmpty(mediaBase.getArea())) {
			MediaLibraryInformationSourceBean library = new MediaLibraryInformationSourceBean();
			mediaLibraryDTO.setArea(library.cleanArea(mediaBase.getArea()));
		}
		if (CollUtil.isNotEmpty(mediaBase.getCodeList())) {
			informationSourceBean.setCodeList(mediaBase.getCodeList());
		}
		ReflectionUtils.copyProperties(informationSourceBean, mediaLibraryDTO);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), WebSpiderNewDataUrlEnum.ES_SEARCH.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).search(informationSourceBean);
			 setResult(result);
		} catch (Exception e) {
			log.error("ElasticSearchAPI search error.",e);
			systemAlert("数据综合查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	}




	/**
	 * 新增或修改
	 * **/
	public  JSONObject saveOrUpdate(MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean) {
		String url = WebSpiderNewDataUrlEnum.ES_INSERT.getUrl();
		if(mediaLibraryInformationSourceBean.getRequestType() != null && mediaLibraryInformationSourceBean.getRequestType().equals(1)) {
			url = WebSpiderNewDataUrlEnum.ES_UPDATE.getUrl();
		}
		mediaLibraryInformationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), url);
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).saveOrUpdate(mediaLibraryInformationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI saveOrUpdate error.",e);
			systemAlert("新增或修改异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	};

	/**
	 *删除
	 * **/
	public  JSONObject delete(MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean) {
		mediaLibraryInformationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_DELETE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).delete(mediaLibraryInformationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI delete error.",e);
			systemAlert("数据删除异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	}


	/**
	 *
	 * 根据id查询
	 * ***/
	public  JSONObject searchById(MediaBaseDTO  mediaBase) {
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_SEARCH_MID.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).search(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI searchById error.",e);
			systemAlert("根据id查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	}

	public JSONObject getSiteInfoById(Integer siteId) {
		String apiUrl = String.format("%s/traditional/informationByid?k3_id=%s", webCrawlerConfig.getTraditionalSiteServer(), siteId);
		JSONObject resultJson = new JSONObject();
		try {
			resultJson = ISearchDataAccess.getAndHasData(apiUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultJson;
	}

	public JSONObject selectMediaTypeTree(MediaBaseDTO mediaBase) {
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_MEDIA_TYPE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).searchMediaType(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI selectMediaTypeTree error.",e);
			systemAlert("获取所有媒体类型树查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	public JSONObject selectInformation(MediaBaseDTO mediaBase) {
		WeMediaDTO weMediaDTO = (WeMediaDTO) mediaBase;
		InformationSourceBean informationSourceBean = setInformationSourceBean(weMediaDTO);

		// 构建JSON请求体
		JSONObject requestBody = buildSearchRequestBody(weMediaDTO);

		// 设置服务URL（不再包含查询参数）
		String source = StringUtils.isEmpty(weMediaDTO.getSourceType()) ? "微信" : weMediaDTO.getSourceType();
		String sourceType = source;
		if(source.equals("今日头条")){
			sourceType = "头条";
		}

		// 使用基础URL进行POST请求
		String baseUrl = WebSpiderNewDataUrlEnum.ES_SEARCH_INFORMATION.getUrl();
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), baseUrl);

		JSONObject result = new JSONObject();
		try {
			// 使用新的POST请求方法
			result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
					.searchInformationSource(informationSourceBean, requestBody);
			setResult(result, source);
		} catch (Exception e) {
			log.error("ElasticSearchAPI selectInformation error.", e);
			systemAlert("信息源查询异常  \n" + e);
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 构建搜索请求体
	 * @param weMediaDTO 查询条件
	 * @return JSON请求体
	 */
	private JSONObject buildSearchRequestBody(WeMediaDTO weMediaDTO) {
		// 构建condition对象
		JSONObject condition = new JSONObject();

		// 基础字段映射
		if (weMediaDTO.getK3Id() != null) {
			condition.put("k3Id", weMediaDTO.getK3Id().toString());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getMediaTag())) {
			condition.put("mediaTag", weMediaDTO.getMediaTag());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getK3IdName())) {
			condition.put("k3IdName", weMediaDTO.getK3IdName());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getOrganizer())) {
			condition.put("organizer", weMediaDTO.getOrganizer());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getName())) {
			condition.put("name", weMediaDTO.getName());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getUid())) {
			condition.put("uid", weMediaDTO.getUid());
		}

		// 媒体相关字段
		if (!StringUtils.isEmpty(weMediaDTO.getAccountLevel())) {
			// mediaInfoTag: 0=非媒体, 1=媒体，根据accountLevel判断
			condition.put("mediaInfoTag", StringUtils.isEmpty(weMediaDTO.getAccountLevel()) ? 0 : 1);
		}
		if (!StringUtils.isEmpty(weMediaDTO.getAccountLevel())) {
			condition.put("mediaLevel", weMediaDTO.getAccountLevel());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getWeiboVerifyType())) {
			condition.put("weiboVerifyType", weMediaDTO.getWeiboVerifyType());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getVerifyType())) {
			condition.put("verifyType", weMediaDTO.getVerifyType());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getIndustry())) {
			condition.put("industry", weMediaDTO.getIndustry());
		}

		// 粉丝数范围
		if (!StringUtils.isEmpty(weMediaDTO.getFollowersCountRangeFrom())) {
			try {
				condition.put("followersCountRangeFrom", Integer.parseInt(weMediaDTO.getFollowersCountRangeFrom()));
			} catch (NumberFormatException e) {
				log.warn("Invalid followersCountRangeFrom: " + weMediaDTO.getFollowersCountRangeFrom());
			}
		}
		if (!StringUtils.isEmpty(weMediaDTO.getFollowersCountRangeTo())) {
			try {
				condition.put("followersCountRangeTo", Integer.parseInt(weMediaDTO.getFollowersCountRangeTo()));
			} catch (NumberFormatException e) {
				log.warn("Invalid followersCountRangeTo: " + weMediaDTO.getFollowersCountRangeTo());
			}
		}

		// 大V标签
		if (!StringUtils.isEmpty(weMediaDTO.getBigVLabel())) {
			try {
				condition.put("bigVLabel", Integer.parseInt(weMediaDTO.getBigVLabel()));
			} catch (NumberFormatException e) {
				log.warn("Invalid bigVLabel: " + weMediaDTO.getBigVLabel());
			}
		}

		// 地理位置
		if (weMediaDTO.getIpLocation() != null) {
			condition.put("ipLocation", weMediaDTO.getIpLocation().toString());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getArea())) {
			condition.put("area", weMediaDTO.getArea());
		}

		// 状态
		if (weMediaDTO.getStatus() != null) {
			condition.put("status", weMediaDTO.getStatus());
		}

		// 其他字段 - 从MediaBaseDTO继承的字段
		if (!StringUtils.isEmpty(weMediaDTO.getAccountType())) {
			condition.put("accountType", weMediaDTO.getAccountType());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getVerifiedInfo())) {
			condition.put("verifiedInfo", weMediaDTO.getVerifiedInfo());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getCode())) {
			condition.put("code", weMediaDTO.getCode());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getBusinessCode())) {
			condition.put("businessCode", weMediaDTO.getBusinessCode());
		}
		if (weMediaDTO.getRefreshType() != null) {
			condition.put("refreshType", weMediaDTO.getRefreshType());
		}

		// 添加其他可能的字段
		if (!StringUtils.isEmpty(weMediaDTO.getMedia())) {
			condition.put("media", weMediaDTO.getMedia());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getMedia_type())) {
			condition.put("media_type", weMediaDTO.getMedia_type());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getMediaLevel())) {
			condition.put("mediaLevel", weMediaDTO.getMediaLevel());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getIndustry())) {
			condition.put("industry", weMediaDTO.getIndustry());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getWeb_url())) {
			condition.put("web_url", weMediaDTO.getWeb_url());
		}
		if (!StringUtils.isEmpty(weMediaDTO.getHomeUrl())) {
			condition.put("homeUrl", weMediaDTO.getHomeUrl());
		}

		// 构建paginator对象
		JSONObject paginator = new JSONObject();
		Integer page = weMediaDTO.getPageNo() == null ? 0 : weMediaDTO.getPageNo() - 1;
		Integer size = weMediaDTO.getSize() == null ? 20 : weMediaDTO.getSize();

		paginator.put("from", page * size);
		paginator.put("size", size);

		// 构建排序
		JSONArray sorts = new JSONArray();
		JSONObject sort = new JSONObject();

		String sortField = !StringUtils.isEmpty(weMediaDTO.getSortField()) ? weMediaDTO.getSortField() : "created_at";
		String sortOrder = !StringUtils.isEmpty(weMediaDTO.getSort()) ? weMediaDTO.getSort() : "desc";

		sort.put("field", sortField);
		sort.put("order", sortOrder);
		sorts.add(sort);

		paginator.put("sorts", sorts);

		// 构建完整请求体
		JSONObject requestBody = new JSONObject();
		requestBody.put("condition", condition);
		requestBody.put("paginator", paginator);

		return requestBody;
	}

	private void setResult(JSONObject result,String source) {
		Map<String, String> mediaType = mediaService.mediaType();
		if(StringUtils.isEmpty(result)) {
			return;
		}
		JSONArray array = result.getJSONArray("data");
		if(StringUtils.isEmpty(array)) {
			return;
		}
		for (int i = 0; i < array.size(); i++) {
			JSONObject json = array.getJSONObject(i);
			json.put("sourceType", source);
			Object code = null == json.get("code") ? mediaType.get(json.getString("accountLevel")) : json.get("code");
			json.put("code", code);
			if(SourceTypeMenu.WE_CHAT.getValue().equals(source)) {
				String homeUrl = "https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz="+json.getString("uid");
				json.put("homeUrl", homeUrl);
			}

			OperationLog logs = operationLogService.selectOperationLogByUid(json.getString("uid"));
			if(logs != null) {
				json.put("operationLog", logs);
			}
			json.put("verifiedType", null);
			if (json.getString("code") != null && json.getString("code").startsWith("101") && json.getString("code").contains(":")) {
				String secondCode = json.getString("code").substring(0, 6);
				json.put("verifiedType", MediaCodeEnum.getNameByCode(secondCode));
			}
			json.put("mediaType", MediaCodeEnum.getNameByCode(json.getString("code")));
		}
	}

	private void setResult(JSONObject result) {
		Map<String,String> mediaType = mediaService.mediaTypeMap();
		if(StringUtils.isEmpty(result)) {
			return;
		}
		JSONArray array = result.getJSONArray("data");
		if(StringUtils.isEmpty(array)) {
			return;
		}
		for (int i = 0; i < array.size(); i++) {
			JSONObject json = array.getJSONObject(i);
			json.put("media_type", mediaType.get(json.getString("code")));
			if (StringUtil.isEmpty(json.getString("code")) || !json.getString("code").contains(":")) {
				json.put("second_level_type", mediaType.get(json.getString("code")));
			} else {
				json.put("second_level_type", mediaType.get(json.getString("code").substring(0, 6)));
			}
			json.put("type", ArticleTypeEnum.getArticleTypeValueByMediaCode(json.getString("code").substring(0, 3)));
			String uid = MD5.encode(json.getString("web_url"));
			json.put("uid", uid);
			OperationLog logs = operationLogService.selectOperationLogByUid(uid);
			if(logs != null) {
				json.put("operationLog", logs);
			}

		}

	}

	private InformationSourceBean setInformationSourceBean(MediaBaseDTO mediaBase) {
		InformationSourceBean informationSourceBean = new InformationSourceBean();
		ReflectionUtils.copyProperties(informationSourceBean, mediaBase);
		return informationSourceBean;
	}


	private void systemAlert(String message) {
		systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_ELASTIC_SEARCH, message);
	}

	public JSONObject deleteWeMedia(WeMediaDeleteDTO deleteDTO) {
		String apiUrl = String.format("%s%s", webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_WE_MEDIA_DELETE.getUrl());
		JSONObject result = new JSONObject();
		try {
			result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).deleteWeMedia(apiUrl, deleteDTO);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("ElasticSearchAPI deleteWeMedia error.",e);
			systemAlert("自媒体删除异常  \n"+e);
		}
		return result;
	}

	public JSONObject WeMediaSaveOrUpdate(WeMediaInformationSourceBean informationSourceBean) {

		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_WE_MEDIA_INSERT_OR_UPDATE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).weMediaSaveOrUpdate(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI saveOrUpdate error.",e);
			systemAlert("自媒体新增或修改异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	public JSONObject weMediaBatchSaveOrUpdate(WeMediaInformationSourceBean informationSourceBean) {
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_WE_MEDIA_INSERT_OR_UPDATE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).weMediaSaveOrUpdate(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI saveOrUpdate error.",e);
			systemAlert("自媒体新增或修改异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	public JSONObject getWeMediaPlatformType(MediaBaseDTO mediaBase) {
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), WebSpiderNewDataUrlEnum.ES_ALL_WE_MEDIA_PLATFORM_TYPE.getUrl());
		JSONObject result = new JSONObject();
		try {
			result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).searchMediaType(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI getWeMediaPlatformType error.", e);
			systemAlert("获取全部的自媒体平台类型查询异常  \n" + e);
		}
		return result;
	}

	public Page<AccountInfoVo> selectAccountPage(WeMediaDTO weMediaDTO) {
		Page<AccountInfoVo> pageResult=new Page<>();
		if(StrUtil.isEmpty(weMediaDTO.getName())){
			return pageResult;
		}
		Map<String,String> mediaTypeMap=new HashMap<>();
		mediaTypeMap.put("微信","0");mediaTypeMap.put("抖音.APP","20382");mediaTypeMap.put("微博","1");
		mediaTypeMap.put("头条","10");mediaTypeMap.put("小红书","21583");mediaTypeMap.put("视频号","33230");
		weMediaDTO.setMedia_type(mediaTypeMap.get(weMediaDTO.getSourceType()));
		List<AccountInfoVo> records = searchFullLibAccount(weMediaDTO);
		for (AccountInfoVo item : records) {
			item.setType(Integer.parseInt(weMediaDTO.getMedia_type()));
		}
		pageResult.setRecords(records);
		pageResult.setTotal(records.size());
		return pageResult;
	}

	private List<AccountInfoVo> searchFullLibAccount(WeMediaDTO weMediaDTO) {
		Integer page =  weMediaDTO.getPageNo() == null ? 1 : weMediaDTO.getPageNo();
		String url= String.format(FullLibAccountPageUrl, weMediaDTO.getMedia_type(),page);
		String resStr=null;
		List<AccountInfoVo> result=new ArrayList<>();
		try{
			if(StrUtil.isNotEmpty(weMediaDTO.getName())){
				url+="&name="+weMediaDTO.getName();
			}
			HttpRequest post = HttpUtil.createPost(url);
			HttpResponse httpResponse = post.execute();
			resStr=httpResponse.body();
			JSONObject resObj = JSON.parseObject(resStr);
			JSONArray data = resObj.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				JSONObject jsonObject = data.getJSONObject(i);
				AccountInfoVo item=new AccountInfoVo();
				item.setUid(jsonObject.getString("uid"));
				item.setHomeUrl(jsonObject.getString("url"));
				item.setPlatName(weMediaDTO.getSourceType());
				item.setAccount(jsonObject.getString("accout"));
				item.setName(jsonObject.getString("name"));
				result.add(item);
			}
		}catch (Exception e){
			log.error("searchFullLibAccount 异常",e);
		}finally {
			log.info("url="+url);
			log.info("param="+JSON.toJSONString(weMediaDTO));
			log.info("resStr="+resStr);
		}
		return result;
	}

}
