package com.izhonghong.ubc.information.service.impl.abstracts;

import javax.management.openmbean.InvalidKeyException;

import com.alibaba.fastjson.JSONArray;
import com.izhonghong.ubc.information.common.annotation.Json;
import com.izhonghong.ubc.information.entity.dto.WeMediaDeleteDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.entity.MediaLibraryInformationSourceBean;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.service.WebSpiderInformationSourceDataService;
import com.izhonghong.ubc.information.util.components.ISearchDataAccess;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class WebSpiderInformationSourceDataServiceImpl extends WebSpiderInformationSourceDataService{

	@Override
	public JSONObject search(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postAndHasData(informationSourceBean.getServiceUrl(),informationSourceBean.paramBody());
		return result;
	}

	@Override
	public JSONObject saveOrUpdate(InformationSourceBean informationSourceBean) throws Exception {
		MediaLibraryInformationSourceBean bean = (MediaLibraryInformationSourceBean) informationSourceBean;
		isNotNull(StringUtils.isEmpty(bean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postJson(bean.getServiceUrl(),bean.getMediaLibraryList());
		return result;
	}

	@Override
	public JSONObject delete(InformationSourceBean informationSourceBean) throws Exception {
		MediaLibraryInformationSourceBean bean = (MediaLibraryInformationSourceBean) informationSourceBean;
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postJson(informationSourceBean.getServiceUrl(), bean.getMediaLibraryList());
		return result;
	}

	@Override
	public JSONObject searchById(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postJson(informationSourceBean.getServiceUrl(),informationSourceBean.paramBody());
		return result;
	}


	@Override
	public JSONObject searchMediaType(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.getAndHasData(informationSourceBean.getServiceUrl());
		return result;
	}
	private void isNotNull(boolean flag){
		if (flag) {
			throw new InvalidKeyException(" Missing key parameters ");
		}
	}

	@Override
	public JSONObject searchInformationSource(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.getAndHasData(informationSourceBean.getServiceUrl());
 		return result;
	}

	@Override
	public JSONObject weMediaSaveOrUpdate(InformationSourceBean informationSourceBean) throws Exception{
		WeMediaInformationSourceBean bean = (WeMediaInformationSourceBean) informationSourceBean;
		isNotNull(StringUtils.isEmpty(bean.getServiceUrl()));
		JSONObject weMediaParamBody = bean.getWeMediaParamBody();
		JSONObject jsonObject = new JSONObject();
		JSONArray objects = new JSONArray();
		objects.add(weMediaParamBody);
		jsonObject.put("params", objects);
		jsonObject.put("refreshPolicy",1);
		JSONObject result  = ISearchDataAccess.postJson(bean.getServiceUrl(),bean.getWeMediaParamBody());
		return result;
	}

	@Override
	public JSONObject deleteWeMedia(String apiUrl, WeMediaDeleteDTO mediaDeleteDTO) throws Exception {
		JSONObject result  = ISearchDataAccess.postJson(apiUrl, JSON.parseObject(JSON.toJSONString(mediaDeleteDTO)));
		return result;
	}
}
